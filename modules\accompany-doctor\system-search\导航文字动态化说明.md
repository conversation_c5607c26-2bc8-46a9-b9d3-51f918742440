# 陪诊师相关页面导航文字动态化实现说明

## 功能概述
实现本地陪诊师模块的导航文字动态化，包括列表页面和详情页面的标题根据后台配置动态显示。

## 涉及页面
1. **陪诊师列表页面** (`modules/accompany-doctor/system-search/accompany-list.vue`)
2. **陪诊师详情页面** (`modules/accompany-doctor/system-search/accompany-details.vue`)

## 实现细节

### 1. 首页组件修改 (accompanyDoctorModule.vue)
- 在跳转到列表页面时传递 `employeeTitle` 参数
- 在跳转到详情页面时传递 `employeeTitle` 参数

```javascript
// 跳转到列表页面
@tap="$navto.push('accompanyList',{isShow:true,cityName:cityName,employeeTitle:employeeTitle})"

// 跳转到详情页面
this.$navto.push('accompanyDoctorDetails', { id, employeeTitle: this.employeeTitle })
```

### 2. 陪诊师列表页面 (accompany-list.vue)
#### 新增功能：
- 添加了导航栏，显示动态标题
- 接收 `employeeTitle` 参数并使用
- 搜索框占位符文字动态化
- 跳转到详情页面时传递文案参数

#### 主要修改：
```javascript
// 数据字段
employeeTitle: '本地陪诊师', // 动态文案，默认值

// 接收参数
if(option.employeeTitle){
  this.employeeTitle = decodeURIComponent(option.employeeTitle)
}

// 跳转传参
this.$navto.push('accompanyDoctorDetails', {id, employeeTitle: this.employeeTitle})
```

#### 模板修改：
```html
<!-- 导航栏 -->
<view class="nav-title">{{ employeeTitle }}</view>

<!-- 搜索框占位符 -->
<input :placeholder="'搜索' + employeeTitle.replace('本地', '') + '姓名'">
```

### 3. 陪诊师详情页面 (accompany-details.vue)
#### 主要修改：
- 接收 `employeeTitle` 参数
- 导航栏标题动态化
- 分享标题动态化

```javascript
// 数据字段
employeeTitle: '陪诊师', // 动态文案，默认值

// 接收参数
if(res.employeeTitle){
  this.employeeTitle = decodeURIComponent(res.employeeTitle)
}

// 分享标题
title: this.employeeTitle.replace('本地', '')
```

#### 模板修改：
```html
<!-- 导航栏标题 -->
<view class="top-nav-c">{{ employeeTitle.replace('本地', '') }}</view>
```

## 数据流向
```
首页 accompanyproviderQueryOne 接口
  ↓ 获取 employeeTitle
首页 accompanyDoctorModule 组件
  ↓ 传递 employeeTitle 参数
陪诊师列表页面 (accompany-list.vue)
  ↓ 传递 employeeTitle 参数
陪诊师详情页面 (accompany-details.vue)
```

## 默认行为
- 如果没有传递 `employeeTitle` 参数，使用默认文案：
  - 列表页面：显示"本地陪诊师"
  - 详情页面：显示"陪诊师"
  - 搜索框：显示"搜索陪诊师姓名"

## 文案处理规则
- 导航栏标题：去掉"本地"前缀，如"本地陪诊师" → "陪诊师"
- 搜索占位符：去掉"本地"前缀，如"本地陪诊师" → "搜索陪诊师姓名"
- 分享标题：去掉"本地"前缀

## 注意事项
1. 参数传递时需要使用 `encodeURIComponent` 和 `decodeURIComponent` 处理中文字符
2. 所有页面都保持向下兼容，如果没有传递参数则使用默认文案
3. 导航栏样式已添加，确保在不同设备上的显示效果一致
