<template>
  <view :class="'moduleBox-' + skinColor">
    <view class="understand-accompany moduleBox-doctor" style="margin:20rpx 0rpx 20rpx;">
      <view class="service-project-head">
        <view class="service-project-head-l">{{ doctorTitle }}</view>
        <view class="service-project-head-r" v-if="displayDoctorList.length > 1" @tap="$navto.push('doctorList',{cityName:cityName})">更多<image class="head-r-img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-right-arrows.png'"></image></view>
      </view>
      <template v-if="displayDoctorList.length">
        <view class="depa-doctor-item" v-for="item in displayDoctorList" :key="item.id" @click="handleJumpDoctor(item.id)">
          <view class="doctor-item-box">
            <view class="doctor-item-l">
              <image mode="aspectFit" :src="item.expertPic"></image>
            </view>
            <view class="doctor-item-r">
              <view class="item-r-head">
                <view class="name"><span>{{ item.name }}</span><span>{{ item.post }}</span></view>
              </view>
              <view class="item-r-bott">
                擅长领域：{{ item.introduction }}
              </view>
              <view class="deptName">{{ item.deptName }}</view>
            </view>
          </view>
          <view class="line"></view>
        </view>
      </template>
      <view class="empty" v-else>
        <view class="empty-img"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-empty.png'"></image></view>
        暂无数据~
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      file_ctx: this.file_ctx,
      displayDoctorList: [],
      allDoctorList: [],
      timer: null,
      maxDisplayCount: 3,
      isPageActive: true,
      prevCityName: ''
    }
  },
  props: {
    indexlist: {
      type: Array,
      default: () => []
    },
    cityName: {
      type: String,
      default: ''
    },
    doctorTitle: {
      type: String,
      default: '本地名医'
    }
  },
  watch: {
    indexlist: {
      handler(newVal) {
        if (newVal && newVal.length) {
          this.allDoctorList = [...newVal];
          this.randomizeDisplayList();
        }
      },
      immediate: true
    },
    cityName: {
      handler(newVal, oldVal) {
        if (oldVal && newVal !== oldVal) {
          // 城市变化时，清空医生数据
          this.clearDoctorData();
        }
        this.prevCityName = newVal;
      },
      immediate: true
    }
  },
  mounted() {
    // 设置定时器，每5秒随机切换一次数据
    this.startRandomTimer();

    // 监听页面显示/隐藏事件
    uni.$on('doctorHome:show', this.handlePageShow);
    uni.$on('doctorHome:hide', this.handlePageHide);
  },
  beforeDestroy() {
    // 组件销毁时清除定时器和事件监听
    this.clearRandomTimer();
    uni.$off('doctorHome:show', this.handlePageShow);
    uni.$off('doctorHome:hide', this.handlePageHide);
  },
  methods: {
    // 清空医生数据
    clearDoctorData() {
      console.log('城市变更，清空医生数据');
      this.allDoctorList = [];
      this.displayDoctorList = [];
    },
    handlePageShow() {
      this.isPageActive = true;
      this.startRandomTimer();
    },
    handlePageHide() {
      this.isPageActive = false;
      this.clearRandomTimer();
    },
    handleJumpDoctor(id) {
      this.$navto.push('DoctorDetail', { id })
    },
    startRandomTimer() {
      // 如果页面不活跃，不启动定时器
      if (!this.isPageActive) return;

      // 首先清除可能存在的定时器，避免重复创建
      this.clearRandomTimer();

      // 首次加载时先显示一次
      this.randomizeDisplayList();

      // 设置5秒定时器
      // this.timer = setInterval(() => {
      //   // 如果页面不再活跃，清除定时器
      //   if (!this.isPageActive) {
      //     this.clearRandomTimer();
      //     return;
      //   }
      //   this.randomizeDisplayList();
      // }, 5000);

      console.log('本地名医定时器已启动');
    },
    clearRandomTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
        console.log('本地名医定时器已清除');
      }
    },
    randomizeDisplayList() {
      // 如果页面不活跃，不执行随机显示
      if (!this.isPageActive) return;

      // 如果全部医生数据小于等于最大显示数量，直接显示全部
      if (this.allDoctorList.length <= this.maxDisplayCount) {
        this.displayDoctorList = [...this.allDoctorList];
        return;
      }

      // 随机选择医生数据
      const shuffled = [...this.allDoctorList].sort(() => 0.5 - Math.random());
      this.displayDoctorList = shuffled.slice(0, this.maxDisplayCount);
    },
    // 公开的刷新方法，可以被父组件调用
    refresh() {
      this.randomizeDisplayList();
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../style/blueSkin.scss';

.understand-accompany.moduleBox-doctor {
  margin: 20rpx 0;
  padding: 24rpx 24rpx 0;
  background: #FFFFFF;
  border-radius: 16rpx;

  .service-project-head {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .service-project-head-l {
      display: flex;
      align-items: center;
      font-size: 32rpx;
      color: #1D2029;
      line-height: 44rpx;
    }

    .service-project-head-r {
      display: flex;
      align-items: center;
      font-size: 24rpx;
      color: #868C9C;
      line-height: 34rpx;

      .head-r-img {
        display: flex;
        width: 32rpx;
        height: 32rpx;
      }
    }
  }

  .depa-doctor-item {
    padding: 20upx 0upx;
    background-color: #fff;
    .doctor-item-box {
      display: flex;
      padding-bottom: 20upx;
      .deptName {
        display: none;
      }
      .doctor-item-l {
        width: 140upx;
        height: 100upx;
        image {
          width: 100%;
          height: 100%;
        }
      }
      .doctor-item-r {
        display: flex;
        flex-direction: column;
        flex: 1;
        margin-left: 15upx;
        .item-r-head {
          .name {
            color: #000;
          }
          span {
            margin-left: 10upx;
          }
        }
        .item-r-bott {
          margin-top: 20upx;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
          color: #333;
        }
      }
    }
  }

  .empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50rpx 0;
    width: 100%;
    .empty-img {
      width: 286rpx;
      height: 212rpx;
      margin-bottom: 20rpx;
      .img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
