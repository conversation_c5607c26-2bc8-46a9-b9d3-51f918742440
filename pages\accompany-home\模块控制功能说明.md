# 陪诊首页模块控制功能实现说明

## 功能概述
根据 `accompanyproviderQueryOne` 接口返回的字段，动态控制本地陪诊师、本地名医、热门医院模块的显示和文案。

## 接口字段说明
- `employeeButton`: 本地陪诊师模块开关（1-开启，0-关闭）
- `employeeTitle`: 本地陪诊师模块文案（可选，有值则使用，无值使用默认"本地陪诊师"）
- `doctorButton`: 本地名医模块开关（1-开启，0-关闭）
- `doctorTitle`: 本地名医模块文案（可选，有值则使用，无值使用默认"本地名医"）
- `hotHospitalButton`: 热门医院模块开关（1-开启，0-关闭）

## 实现细节

### 1. 主页面 (pages/accompany-home/index.vue)
- 在 `data()` 中添加了模块控制相关字段
- 在 `onLoad()` 方法中调用 `accompanyproviderQueryOne` 接口获取配置
- 将配置传递给 `moduleBox` 组件

### 2. 模块容器组件 (components/moduleBox.vue)
- 添加了新的 props 接收模块控制参数
- 使用 `v-if` 指令根据开关控制模块显示
- 将文案参数传递给子组件

### 3. 本地名医组件 (components/localDoctorModule.vue)
- 添加 `doctorTitle` prop 接收动态文案
- 在模板中使用 `{{ doctorTitle }}` 显示动态文案

### 4. 本地陪诊师组件 (components/accompanyDoctorModule.vue)
- 添加 `employeeTitle` prop 接收动态文案
- 在模板中使用 `{{ employeeTitle }}` 显示动态文案

## 默认行为
- 如果接口未返回相关字段，所有模块默认开启
- 如果接口未返回文案字段，使用默认文案：
  - 本地陪诊师：默认显示"本地陪诊师"
  - 本地名医：默认显示"本地名医"
  - 热门医院：固定显示"热门医院"（暂无动态文案需求）

## 使用示例
```javascript
// 接口返回示例
{
  employeeButton: 1,
  employeeTitle: "专业陪诊师",
  doctorButton: 0,  // 关闭本地名医模块
  doctorTitle: "权威专家",
  hotHospitalButton: 1
}
```

## 注意事项
1. 模块开关字段使用数字类型：1表示开启，0表示关闭
2. 文案字段为可选，如果有值则使用接口返回的文案，否则使用默认文案
3. 热门医院模块目前只支持开关控制，文案固定为"热门医院"
4. 所有配置在页面加载时获取，如需动态更新需要重新加载页面
